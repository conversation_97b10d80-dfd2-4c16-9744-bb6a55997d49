import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns

# 设置风格
sns.set(style="white", font_scale=1.3)
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['pdf.fonttype'] = 42

# 根据您图片中的数据创建数据集
# 从图片中提取的数据点坐标和垃圾量
data_points = [
    [1, 12, 8, 1.0],    # 点1
    [2, 22, 18, 2.5],   # 点2
    [3, 20, 30, 1.5],   # 点3
    [4, 25, 10, 3.0],   # 点4
    [5, 35, 22, 2.5],   # 点5
    [6, 18, 5, 1.2],    # 点6
    [7, 5, 25, 1.0],    # 点7
    [8, 10, 25, 1.5],   # 点8
    [9, 25, 25, 3.0],   # 点9
    [10, 35, 15, 3.5],  # 点10
    [11, 5, 8, 1.2],    # 点11
    [12, 15, 32, 2.0],  # 点12
    [13, 28, 5, 2.8],   # 点13
    [14, 30, 12, 2.5],  # 点14
    [15, 10, 10, 1.8],  # 点15
    [16, 20, 20, 2.2],  # 点16
    [17, 35, 30, 3.0],  # 点17
    [18, 8, 22, 1.5],   # 点18
    [19, 25, 25, 2.8],  # 点19
    [20, 32, 8, 2.5],   # 点20
    [21, 15, 5, 1.0],   # 点21
    [22, 28, 20, 1.8],  # 点22
    [23, 35, 25, 3.2],  # 点23
    [24, 10, 30, 1.5],  # 点24
    [25, 20, 10, 1.8],  # 点25
    [26, 30, 18, 2.8],  # 点26
    [27, 5, 15, 1.0],   # 点27
    [28, 18, 30, 2.0],  # 点28
    [29, 35, 10, 3.5],  # 点29
    [30, 22, 35, 1.8],  # 点30
]

# 转换为DataFrame
data = pd.DataFrame(data_points, columns=['收集点编号', 'x', 'y', 'w'])

x = data['x'].values
y = data['y'].values
z = np.zeros_like(x)  # 柱子底部
dz = data['w'].values

# 柱子宽度
dx = dy = 1.2  # 可根据点密度调整

# 颜色映射（垃圾量越多颜色越深）
normed_dz = (dz - dz.min()) / (dz.max() - dz.min())
cmap = sns.color_palette("rocket_r", as_cmap=True)  # 反转色带，越多越深
colors = cmap(normed_dz)

fig = plt.figure(figsize=(12, 9))
ax = fig.add_subplot(111, projection='3d')

# 美化三维图背景和坐标轴
ax.set_facecolor('#F7F7F7')  # 图背景为浅灰色
ax.w_xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))  # x轴面白色
ax.w_yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))  # y轴面白色
ax.w_zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))  # z轴面白色
ax.w_xaxis.line.set_linewidth(1.5)
ax.w_yaxis.line.set_linewidth(1.5)
ax.w_zaxis.line.set_linewidth(1.5)

# 去除顶部和右侧边框
ax.w_xaxis._axinfo['grid']['color'] =  (1,1,1,0)
ax.w_yaxis._axinfo['grid']['color'] =  (1,1,1,0)
ax.w_zaxis._axinfo['grid']['color'] =  (1,1,1,0)

# 柱状图
bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='white', linewidth=0.8, alpha=0.95, shade=True)

# 添加数据点标签
for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
    ax.text(xi, yi, dzi + 0.1, str(i+1), fontsize=10, ha='center', va='bottom', fontweight='bold')

# 坐标轴美化
ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
ax.set_title('垃圾分类收集点分布图', fontsize=18, fontweight='bold', pad=20)

# 色条（更细更靠右）
import matplotlib as mpl
m = mpl.cm.ScalarMappable(cmap=cmap)
m.set_array(dz)
cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15, location='right')
cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15, loc='top')
cbar.ax.tick_params(labelsize=12)

# 图例
from matplotlib.patches import Patch
legend_patch = Patch(facecolor=cmap(0.8), edgecolor='white', label='收集点')
ax.legend(handles=[legend_patch], loc='upper left', fontsize=12, frameon=False)

# 视角
ax.view_init(elev=28, azim=120)

plt.tight_layout(pad=2.5)
plt.savefig('垃圾分类点三维柱状图.pdf', dpi=600, bbox_inches='tight')
plt.show()

# 添加热力图
plt.figure(figsize=(9, 7))

# 计算密度
kde = sns.kdeplot(x=data['x'], y=data['y'], cmap='rocket_r', fill=True, thresh=0, levels=100)

# 绘制等高线
x_grid = np.linspace(data['x'].min(), data['x'].max(), 100)
y_grid = np.linspace(data['y'].min(), data['y'].max(), 100)
X, Y = np.meshgrid(x_grid, y_grid)
Z = np.zeros_like(X)

# 计算每个网格点的密度
for i in range(len(X)):
    for j in range(len(Y)):
        Z[i, j] = np.sum((data['x'] - X[i, j])**2 + (data['y'] - Y[i, j])**2 < 1)  # 这里可以根据需求调整

# 绘制等高线
contour = plt.contourf(X, Y, Z, levels=20, cmap='rocket_r', alpha=0.5)
plt.colorbar(contour, label='密度')

plt.title('需求密度热力图', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('X坐标', fontsize=14, fontweight='bold')
plt.ylabel('Y坐标', fontsize=14, fontweight='bold')

# 在热力图上标出每个收集点
plt.scatter(data['x'], data['y'], color='black', s=50, edgecolor='white', label='收集点', alpha=0.7)

plt.legend(loc='upper right', fontsize=12)
plt.tight_layout()
plt.savefig('需求密度热力图.pdf', dpi=600, bbox_inches='tight')
plt.show()