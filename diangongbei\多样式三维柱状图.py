import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D
import matplotlib as mpl

# 设置风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['pdf.fonttype'] = 42

# 根据您图片中的数据创建数据集
data_points = [
    [1, 12, 8, 1.0],    [2, 22, 18, 2.5],   [3, 20, 30, 1.5],   [4, 25, 10, 3.0],   [5, 35, 22, 2.5],
    [6, 18, 5, 1.2],    [7, 5, 25, 1.0],    [8, 10, 25, 1.5],   [9, 25, 25, 3.0],   [10, 35, 15, 3.5],
    [11, 5, 8, 1.2],    [12, 15, 32, 2.0],  [13, 28, 5, 2.8],   [14, 30, 12, 2.5],  [15, 10, 10, 1.8],
    [16, 20, 20, 2.2],  [17, 35, 30, 3.0],  [18, 8, 22, 1.5],   [19, 25, 25, 2.8],  [20, 32, 8, 2.5],
    [21, 15, 5, 1.0],   [22, 28, 20, 1.8],  [23, 35, 25, 3.2],  [24, 10, 30, 1.5],  [25, 20, 10, 1.8],
    [26, 30, 18, 2.8],  [27, 5, 15, 1.0],   [28, 18, 30, 2.0],  [29, 35, 10, 3.5],  [30, 22, 35, 1.8],
]

# 转换为DataFrame
data = pd.DataFrame(data_points, columns=['收集点编号', 'x', 'y', 'w'])

def create_style1_bar_chart():
    """创建样式1：经典三维柱状图"""
    x = data['x'].values
    y = data['y'].values
    z = np.zeros_like(x)
    dz = data['w'].values
    dx = dy = 1.2
    
    # 颜色映射
    normed_dz = (dz - dz.min()) / (dz.max() - dz.min())
    colors = plt.cm.viridis(normed_dz)
    
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 美化背景
    try:
        ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
        ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
        ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    except:
        pass
    
    ax.grid(False)
    
    # 绘制柱状图
    bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='white', 
                    linewidth=0.8, alpha=0.9)
    
    # 添加标签
    for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
        ax.text(xi, yi, dzi + 0.15, str(i+1), fontsize=9, ha='center', 
                va='bottom', fontweight='bold', color='black')
    
    ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
    ax.set_title('垃圾分类收集点分布图（经典风格）', fontsize=18, fontweight='bold', pad=20)
    
    # 色条
    m = mpl.cm.ScalarMappable(cmap=plt.cm.viridis)
    m.set_array(dz)
    cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15)
    cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15)
    
    ax.view_init(elev=25, azim=45)
    plt.tight_layout(pad=2.5)
    plt.savefig('diangongbei/垃圾分类点三维柱状图_经典风格.pdf', dpi=600, bbox_inches='tight')
    plt.savefig('diangongbei/垃圾分类点三维柱状图_经典风格.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_style2_bar_chart():
    """创建样式2：现代简约风格"""
    x = data['x'].values
    y = data['y'].values
    z = np.zeros_like(x)
    dz = data['w'].values
    dx = dy = 1.0
    
    # 使用现代配色
    colors = plt.cm.plasma(np.linspace(0, 1, len(x)))
    
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 设置背景为深色
    fig.patch.set_facecolor('#2E2E2E')
    ax.set_facecolor('#2E2E2E')
    
    try:
        ax.xaxis.set_pane_color((0.2, 0.2, 0.2, 1.0))
        ax.yaxis.set_pane_color((0.2, 0.2, 0.2, 1.0))
        ax.zaxis.set_pane_color((0.2, 0.2, 0.2, 1.0))
    except:
        pass
    
    # 绘制柱状图
    bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='black', 
                    linewidth=0.5, alpha=0.95)
    
    # 添加标签（白色）
    for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
        ax.text(xi, yi, dzi + 0.15, str(i+1), fontsize=9, ha='center', 
                va='bottom', fontweight='bold', color='white')
    
    # 设置坐标轴颜色为白色
    ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold', color='white')
    ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold', color='white')
    ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold', color='white')
    ax.set_title('垃圾分类收集点分布图（现代风格）', fontsize=18, fontweight='bold', pad=20, color='white')
    
    # 设置刻度标签颜色
    ax.tick_params(axis='x', colors='white')
    ax.tick_params(axis='y', colors='white')
    ax.tick_params(axis='z', colors='white')
    
    # 色条
    m = mpl.cm.ScalarMappable(cmap=plt.cm.plasma)
    m.set_array(dz)
    cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15)
    cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15, color='white')
    cbar.ax.tick_params(colors='white')
    
    ax.view_init(elev=30, azim=60)
    plt.tight_layout(pad=2.5)
    plt.savefig('diangongbei/垃圾分类点三维柱状图_现代风格.pdf', dpi=600, bbox_inches='tight', facecolor='#2E2E2E')
    plt.savefig('diangongbei/垃圾分类点三维柱状图_现代风格.png', dpi=300, bbox_inches='tight', facecolor='#2E2E2E')
    plt.close()

def create_style3_bar_chart():
    """创建样式3：学术论文风格"""
    x = data['x'].values
    y = data['y'].values
    z = np.zeros_like(x)
    dz = data['w'].values
    dx = dy = 1.1
    
    # 使用学术配色
    normed_dz = (dz - dz.min()) / (dz.max() - dz.min())
    colors = plt.cm.coolwarm(normed_dz)
    
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 简洁的白色背景
    fig.patch.set_facecolor('white')
    
    try:
        ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
        ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
        ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    except:
        pass
    
    ax.grid(True, alpha=0.3)
    
    # 绘制柱状图
    bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='gray', 
                    linewidth=0.6, alpha=0.85)
    
    # 添加标签
    for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
        ax.text(xi, yi, dzi + 0.1, str(i+1), fontsize=8, ha='center', 
                va='bottom', fontweight='normal', color='black')
    
    ax.set_xlabel('X坐标 (km)', labelpad=12, fontsize=12, fontweight='normal')
    ax.set_ylabel('Y坐标 (km)', labelpad=12, fontsize=12, fontweight='normal')
    ax.set_zlabel('垃圾量 (吨)', labelpad=15, fontsize=12, fontweight='normal')
    ax.set_title('垃圾分类收集点空间分布', fontsize=14, fontweight='bold', pad=15)
    
    # 色条
    m = mpl.cm.ScalarMappable(cmap=plt.cm.coolwarm)
    m.set_array(dz)
    cbar = fig.colorbar(m, ax=ax, shrink=0.6, aspect=30, pad=0.1)
    cbar.set_label('垃圾量 (吨)', fontsize=11, fontweight='normal', labelpad=12)
    
    ax.view_init(elev=20, azim=30)
    plt.tight_layout(pad=1.5)
    plt.savefig('diangongbei/垃圾分类点三维柱状图_学术风格.pdf', dpi=600, bbox_inches='tight')
    plt.savefig('diangongbei/垃圾分类点三维柱状图_学术风格.png', dpi=300, bbox_inches='tight')
    plt.close()

# 生成所有风格的图表
if __name__ == "__main__":
    print("正在生成经典风格三维柱状图...")
    create_style1_bar_chart()
    
    print("正在生成现代风格三维柱状图...")
    create_style2_bar_chart()
    
    print("正在生成学术风格三维柱状图...")
    create_style3_bar_chart()
    
    print("所有风格的三维柱状图生成完成！")
    print("生成的文件：")
    print("- 垃圾分类点三维柱状图_经典风格.pdf/.png")
    print("- 垃圾分类点三维柱状图_现代风格.pdf/.png")
    print("- 垃圾分类点三维柱状图_学术风格.pdf/.png")
