import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

def plot_time_constraint_impact_analysis(time_constraints, transport_costs, carbon_costs, vehicle_counts):
    """
    绘制时间约束对总运输成本、碳排放成本和车辆需求的影响分析图
    
    参数:
    time_constraints: 时间约束列表（小时）
    transport_costs: 对应的总运输成本列表
    carbon_costs: 对应的碳排放成本列表
    vehicle_counts: 对应的车辆数量列表
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 创建图表
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 设置颜色
    color1 = '#3498db'  # 蓝色
    color2 = '#2ecc71'  # 绿色
    color3 = '#e74c3c'  # 红色
    
    # 绘制总运输成本曲线（左Y轴）
    ax1.set_xlabel('时间约束（小时）', fontsize=14, fontweight='bold')
    ax1.set_ylabel('总运输成本（元）', fontsize=14, fontweight='bold', color=color1)
    line1 = ax1.plot(time_constraints, transport_costs, marker='o', markersize=8, 
                    linewidth=3, color=color1, label='总运输成本')
    ax1.tick_params(axis='y', labelcolor=color1)
    
    # 设置总运输成本Y轴范围，确保曲线不交叉
    transport_min = min(transport_costs) * 0.95
    transport_max = max(transport_costs) * 1.05
    ax1.set_ylim(transport_min, transport_max)
    
    # 创建共享X轴的第二个Y轴
    ax2 = ax1.twinx()
    ax2.set_ylabel('碳排放成本（元）', fontsize=14, fontweight='bold', color=color2)
    line2 = ax2.plot(time_constraints, carbon_costs, marker='s', markersize=8, 
                    linewidth=3, color=color2, label='碳排放成本')
    ax2.tick_params(axis='y', labelcolor=color2)
    
    # 设置碳排放成本Y轴范围，确保曲线不交叉
    carbon_min = min(carbon_costs) * 0.95
    carbon_max = max(carbon_costs) * 1.05
    ax2.set_ylim(carbon_min, carbon_max)
    
    # 创建共享X轴的第三个Y轴
    ax3 = ax1.twinx()
    ax3.spines['right'].set_position(('outward', 60))  # 向外偏移60点
    ax3.set_ylabel('车辆数量', fontsize=14, fontweight='bold', color=color3)
    line3 = ax3.plot(time_constraints, vehicle_counts, marker='^', markersize=8, 
                    linewidth=3, color=color3, label='车辆需求量')
    ax3.tick_params(axis='y', labelcolor=color3)
    
    # 设置车辆数量Y轴范围，确保曲线不交叉
    vehicle_min = min(vehicle_counts) * 0.9
    vehicle_max = max(vehicle_counts) * 1.1
    ax3.set_ylim(vehicle_min, vehicle_max)
    
    # 添加数据标签
    for i, (cost, carbon, vehicles) in enumerate(zip(transport_costs, carbon_costs, vehicle_counts)):
        ax1.annotate(f'{cost:.2f}', xy=(time_constraints[i], cost), xytext=(0, 10),
                    textcoords='offset points', ha='center', color=color1, fontweight='bold')
        ax2.annotate(f'{carbon:.2f}', xy=(time_constraints[i], carbon), xytext=(0, -15),
                    textcoords='offset points', ha='center', color=color2, fontweight='bold')
        ax3.annotate(f'{vehicles}', xy=(time_constraints[i], vehicles), xytext=(0, 10),
                    textcoords='offset points', ha='center', color=color3, fontweight='bold')
    
    # 合并图例
    lines = line1 + line2 + line3
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15),
              ncol=3, fontsize=12, frameon=True)
    
    # 设置标题
    plt.title('时间约束对总运输成本、碳排放成本和车辆需求的影响分析', fontsize=18, fontweight='bold', pad=20)
    
    # 添加网格线
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 美化图表
    sns.despine(left=False, bottom=False, right=False)
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('时间约束影响分析图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 示例数据（根据实际数据替换）
time_constraints = [6, 8, 10, 12, 14]
transport_costs = [3500, 3300, 3100, 2950, 2900]
carbon_costs = [520, 500, 470, 450, 440]
vehicle_counts = [18, 15, 12, 10, 9]

# 绘制图表
plot_time_constraint_impact_analysis(time_constraints, transport_costs, carbon_costs, vehicle_counts)
