import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import matplotlib as mpl

# 设置风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['pdf.fonttype'] = 42

# 根据您图片中的数据创建数据集
# 从图片中提取的数据点坐标和垃圾量
data_points = [
    [1, 12, 8, 1.0],    [2, 22, 18, 2.5],   [3, 20, 30, 1.5],   [4, 25, 10, 3.0],   [5, 35, 22, 2.5],
    [6, 18, 5, 1.2],    [7, 5, 25, 1.0],    [8, 10, 25, 1.5],   [9, 25, 25, 3.0],   [10, 35, 15, 3.5],
    [11, 5, 8, 1.2],    [12, 15, 32, 2.0],  [13, 28, 5, 2.8],   [14, 30, 12, 2.5],  [15, 10, 10, 1.8],
    [16, 20, 20, 2.2],  [17, 35, 30, 3.0],  [18, 8, 22, 1.5],   [19, 25, 25, 2.8],  [20, 32, 8, 2.5],
    [21, 15, 5, 1.0],   [22, 28, 20, 1.8],  [23, 35, 25, 3.2],  [24, 10, 30, 1.5],  [25, 20, 10, 1.8],
    [26, 30, 18, 2.8],  [27, 5, 15, 1.0],   [28, 18, 30, 2.0],  [29, 35, 10, 3.5],  [30, 22, 35, 1.8],
]

# 转换为DataFrame
data = pd.DataFrame(data_points, columns=['收集点编号', 'x', 'y', 'w'])

# 提取数据
x = data['x'].values
y = data['y'].values
z = np.zeros_like(x)  # 柱子底部
dz = data['w'].values

# 柱子宽度
dx = dy = 1.2

# 颜色映射（垃圾量越多颜色越深）
normed_dz = (dz - dz.min()) / (dz.max() - dz.min())
colors = plt.cm.viridis(normed_dz)

# 创建图形
fig = plt.figure(figsize=(14, 10))
ax = fig.add_subplot(111, projection='3d')

# 美化三维图背景和坐标轴
try:
    ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
except:
    pass

# 去除网格
ax.grid(False)

# 绘制柱状图
bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='white',
                linewidth=0.8, alpha=0.95)

# 添加数据点标签
for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
    ax.text(xi, yi, dzi + 0.15, str(i+1), fontsize=9, ha='center',
            va='bottom', fontweight='bold', color='black')

# 坐标轴美化
ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
ax.set_title('垃圾分类收集点分布图', fontsize=18, fontweight='bold', pad=20)

# 色条
m = mpl.cm.ScalarMappable(cmap=plt.cm.viridis)
m.set_array(dz)
cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15)
cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15)
cbar.ax.tick_params(labelsize=12)

# 设置视角
ax.view_init(elev=25, azim=45)

# 保存图形
plt.tight_layout(pad=2.5)
plt.savefig('diangongbei/垃圾分类点三维柱状图.pdf', dpi=600, bbox_inches='tight')
plt.savefig('diangongbei/垃圾分类点三维柱状图.png', dpi=300, bbox_inches='tight')
# plt.show()  # 注释掉显示，避免阻塞

print("三维柱状图生成完成！文件已保存为 '垃圾分类点三维柱状图.pdf' 和 '垃圾分类点三维柱状图.png'")
