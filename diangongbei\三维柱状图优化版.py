import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from matplotlib.patches import Patch
import matplotlib as mpl

# 设置风格
sns.set_theme(style="white", font_scale=1.3)
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['pdf.fonttype'] = 42

# 根据您图片中的数据创建数据集
# 从图片中提取的数据点坐标和垃圾量
data_points = [
    [1, 12, 8, 1.0],    [2, 22, 18, 2.5],   [3, 20, 30, 1.5],   [4, 25, 10, 3.0],   [5, 35, 22, 2.5],
    [6, 18, 5, 1.2],    [7, 5, 25, 1.0],    [8, 10, 25, 1.5],   [9, 25, 25, 3.0],   [10, 35, 15, 3.5],
    [11, 5, 8, 1.2],    [12, 15, 32, 2.0],  [13, 28, 5, 2.8],   [14, 30, 12, 2.5],  [15, 10, 10, 1.8],
    [16, 20, 20, 2.2],  [17, 35, 30, 3.0],  [18, 8, 22, 1.5],   [19, 25, 25, 2.8],  [20, 32, 8, 2.5],
    [21, 15, 5, 1.0],   [22, 28, 20, 1.8],  [23, 35, 25, 3.2],  [24, 10, 30, 1.5],  [25, 20, 10, 1.8],
    [26, 30, 18, 2.8],  [27, 5, 15, 1.0],   [28, 18, 30, 2.0],  [29, 35, 10, 3.5],  [30, 22, 35, 1.8],
]

# 转换为DataFrame
data = pd.DataFrame(data_points, columns=['收集点编号', 'x', 'y', 'w'])

def create_3d_bar_chart():
    """创建三维柱状图"""
    x = data['x'].values
    y = data['y'].values
    z = np.zeros_like(x)  # 柱子底部
    dz = data['w'].values

    # 柱子宽度
    dx = dy = 1.2

    # 颜色映射
    normed_dz = (dz - dz.min()) / (dz.max() - dz.min())
    cmap = sns.color_palette("rocket_r", as_cmap=True)
    colors = cmap(normed_dz)

    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 美化三维图背景和坐标轴
    ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.xaxis.line.set_linewidth(1.5)
    ax.yaxis.line.set_linewidth(1.5)
    ax.zaxis.line.set_linewidth(1.5)

    # 去除网格
    ax.grid(False)

    # 柱状图
    bars = ax.bar3d(x, y, z, dx, dy, dz, color=colors, edgecolor='white',
                    linewidth=0.8, alpha=0.95, shade=True)

    # 添加数据点标签
    for i, (xi, yi, dzi) in enumerate(zip(x, y, dz)):
        ax.text(xi, yi, dzi + 0.15, str(i+1), fontsize=9, ha='center',
                va='bottom', fontweight='bold', color='black')

    # 坐标轴美化
    ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
    ax.set_title('垃圾分类收集点分布图', fontsize=18, fontweight='bold', pad=20)

    # 色条
    m = mpl.cm.ScalarMappable(cmap=cmap)
    m.set_array(dz)
    cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15, location='right')
    cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15, loc='top')
    cbar.ax.tick_params(labelsize=12)

    # 视角
    ax.view_init(elev=25, azim=45)

    plt.tight_layout(pad=2.5)
    plt.savefig('垃圾分类点三维柱状图.pdf', dpi=600, bbox_inches='tight')
    plt.show()

def create_3d_stem_plot():
    """创建三维茎线图"""
    x = data['x'].values
    y = data['y'].values
    z = data['w'].values

    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 美化背景
    ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))

    # 颜色映射
    normed_z = (z - z.min()) / (z.max() - z.min())
    cmap = sns.color_palette("viridis", as_cmap=True)
    colors = cmap(normed_z)

    # 绘制茎线
    for i, (xi, yi, zi) in enumerate(zip(x, y, z)):
        ax.plot([xi, xi], [yi, yi], [0, zi], color=colors[i], linewidth=3, alpha=0.8)
        ax.scatter(xi, yi, zi, color=colors[i], s=100, alpha=0.9, edgecolor='white', linewidth=1)
        ax.text(xi, yi, zi + 0.1, str(i+1), fontsize=9, ha='center', va='bottom', fontweight='bold')

    ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
    ax.set_title('垃圾分类点三维茎线图', fontsize=18, fontweight='bold', pad=20)

    # 色条
    m = mpl.cm.ScalarMappable(cmap=cmap)
    m.set_array(z)
    cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15)
    cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15)

    ax.view_init(elev=25, azim=45)
    plt.tight_layout(pad=2.5)
    plt.savefig('垃圾分类点三维茎线图.pdf', dpi=600, bbox_inches='tight')
    plt.show()

def create_3d_surface_plot():
    """创建三维山脊图"""
    x = data['x'].values
    y = data['y'].values
    z = data['w'].values

    # 创建网格
    xi = np.linspace(x.min(), x.max(), 50)
    yi = np.linspace(y.min(), y.max(), 50)
    Xi, Yi = np.meshgrid(xi, yi)

    # 插值
    from scipy.interpolate import griddata
    Zi = griddata((x, y), z, (Xi, Yi), method='cubic')

    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 美化背景
    ax.xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
    ax.zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))

    # 绘制表面
    surf = ax.plot_surface(Xi, Yi, Zi, cmap='viridis', alpha=0.8, linewidth=0, antialiased=True)

    # 添加原始数据点
    ax.scatter(x, y, z, color='red', s=50, alpha=0.9, edgecolor='white', linewidth=1)

    # 添加标签
    for i, (xi, yi, zi) in enumerate(zip(x, y, z)):
        ax.text(xi, yi, zi + 0.1, str(i+1), fontsize=8, ha='center', va='bottom', fontweight='bold')

    ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
    ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
    ax.set_title('垃圾分类点三维山脊图', fontsize=18, fontweight='bold', pad=20)

    # 色条
    cbar = fig.colorbar(surf, ax=ax, shrink=0.5, aspect=40, pad=0.15)
    cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15)

    ax.view_init(elev=25, azim=45)
    plt.tight_layout(pad=2.5)
    plt.savefig('垃圾分类点三维山脊图.pdf', dpi=600, bbox_inches='tight')
    plt.show()

# 生成所有图表
if __name__ == "__main__":
    print("正在生成三维柱状图...")
    create_3d_bar_chart()

    print("正在生成三维茎线图...")
    create_3d_stem_plot()

    print("正在生成三维山脊图...")
    create_3d_surface_plot()

    print("所有图表生成完成！")
